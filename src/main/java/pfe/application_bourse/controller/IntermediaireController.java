package pfe.application_bourse.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import pfe.application_bourse.dto.ordre.*;
import pfe.application_bourse.dto.referentiel.ActionDTO;
import pfe.application_bourse.entity.User;
import pfe.application_bourse.entity.ordre.Operation;
import pfe.application_bourse.entity.ordre.Ordre;
import pfe.application_bourse.enumeration.StatutType;
import pfe.application_bourse.mapper.OperationMapper;
import pfe.application_bourse.repository.ordre.OperationRepository;
import pfe.application_bourse.repository.ordre.OrdreRepository;
import pfe.application_bourse.service.ActionService;
import pfe.application_bourse.service.ordre.IntermediaireService;
import pfe.application_bourse.service.ordre.OperationService;
import pfe.application_bourse.service.ordre.OrdreProposeService;
import pfe.application_bourse.service.ordre.OrdreService;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/intermediaire")
@RequiredArgsConstructor
public class IntermediaireController {
    private final OrdreProposeService ordreProposeService;
    private final IntermediaireService intermediaireService;
private final OrdreService ordreService;
private final OperationService operationService;
private final OperationRepository operationRepository;
private final OrdreRepository ordreRepository;
private final ActionService actionService;
    @GetMapping("/par-investisseur/{username}")
    public List<OrdreProposeResponseDTO> getPropositionsPourInvestisseur(@PathVariable String username) {
        return ordreProposeService.getPropositionsParUsername(username);
    }
    @GetMapping("/par-investisseur/{username}/type/{type}")
    public List<OrdreProposeResponseDTO> getPropositionsParInvestisseurEtType(
            @PathVariable String username,
            @PathVariable String type
    ) {
        return ordreProposeService.getPropositionsParInvestisseurEtType(username, type);
    }

    @GetMapping("/OrdresProposition")
    public List<OrdreProposeResponseDTO> getAllOrdresProposes() {
        return ordreProposeService.getAllPropositions();
    }
    @GetMapping("/clients")
    public List<InvestisseurDTO> getInvestisseurs() {
        return intermediaireService.getTousLesInvestisseurs();
    }
    @PostMapping("/simulation")
    public ResponseEntity<OrdreProposeSimulationResponseDTO> simulerOrdre(@RequestBody OrdreProposeSimulationRequest request) {
        return ResponseEntity.ok(intermediaireService.simulerOrdre(request));
    }
    @PostMapping("/approuver")
    public ResponseEntity<?> approuverOrdre(@RequestBody OrdreProposeSimulationRequest request) {
        intermediaireService.approuverOrdre(request.getOrdreProposeId(), request.getPrixRef());
        return ResponseEntity.ok().build();
    }
    @PostMapping(
            value = "/refuser",
            consumes = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<?> refuserOrdre(@RequestBody Request request) {
        intermediaireService.refuserOrdre(request.getOrdreProposeId());
        return ResponseEntity.ok().build();
    }
    @GetMapping("/ordres/valides")
    public ResponseEntity<List<OrdreResponseDTO>> getOrdresValides() {
        List<OrdreResponseDTO> ordres = ordreService.getOrdresValides();
        return ResponseEntity.ok(ordres);
    }
    @GetMapping("/ordres/enExecution")
    public ResponseEntity<List<OrdreResponseDTO>> getOrdresEnExecution() {
        List<OrdreResponseDTO> ordres = ordreService.getOrdresEnExecution();
        return ResponseEntity.ok(ordres);
    }
    @GetMapping("/ordres/execute")
    public ResponseEntity<List<OrdreResponseDTO>> getOrdresExecute() {
        List<OrdreResponseDTO> ordres = ordreService.getOrdresExecute();
        return ResponseEntity.ok(ordres);
    }
    @GetMapping("/ordres/{id}/similaires")
    public ResponseEntity<List<OrdreResponseDTO>> getOrdresSimilaires(@PathVariable UUID id) {
        List<OrdreResponseDTO> similaires = ordreService.getOrdresSimilaires(id);
        return ResponseEntity.ok(similaires);
    }

    @PostMapping("/regrouper-ordres")
    public ResponseEntity<Operation> regrouperOrdres(@RequestBody UUID ordreId) {
        Operation result = ordreService.regrouperOrdresSimilairesDansOperation(ordreId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/ordres/{ordreId}/simuler-fractionnement")
    public ResponseEntity<List<OperationSimulationDTO>> simulerFractionnement(@PathVariable UUID ordreId) {
        List<OperationSimulationDTO> simulations = ordreService.simulerFractionnement(ordreId);
        return ResponseEntity.ok(simulations);
    }

    @PostMapping("/ordres/{ordreId}/valider-fractionnement/{indexEnvoye}")
    public ResponseEntity<List<OperationResponseDTO>> validerEtEnvoyerFractionnement(
            @PathVariable UUID ordreId,
            @PathVariable int indexEnvoye
    ) {
        List<OperationResponseDTO> resultat = ordreService.validerEtEnvoyerFractionnement(ordreId, indexEnvoye);
        return ResponseEntity.ok(resultat);
    }
    @GetMapping("/operations/execution-groupes")
    public ResponseEntity<List<OperationExecutionGroupDTO>> getGroupesExecution() {
        List<OperationExecutionGroupDTO> result = operationService.getOperationsEnExecutionAvecLiens();
        return ResponseEntity.ok(result);
    }
    @GetMapping("/kpi")
    public ResponseEntity<Map<String, Object>> getKpi() {
        return ResponseEntity.ok(intermediaireService.getKpiDashboard());
    }

    @PostMapping("/envoyer-notification")
    public ResponseEntity<String> envoyerNotification(@RequestBody NotificationRequestDTO dto) {
        return ResponseEntity.ok(intermediaireService.envoyerNotification(dto));
    }

    @GetMapping("/historique")
    public ResponseEntity<List<HistoriqueExecutionDTO>> getHistoriqueExecution() {
        return ResponseEntity.ok(intermediaireService.getHistoriqueExecution());
    }
    @GetMapping("/top-actions-echangees")
    public ResponseEntity<List<Map<String, Object>>> getTop5ActionsEchangees() {
        return ResponseEntity.ok(intermediaireService.getTop5ActionsEchangees());
    }

    @GetMapping("/moyenne-operations-par-ordre")
    public ResponseEntity<Double> getMoyenneOperationsParOrdre() {
        return ResponseEntity.ok(intermediaireService.getMoyenneOperationsParOrdre());
    }

    @GetMapping("/ratio-execution-valides")
    public ResponseEntity<Double> getRatioExecutionSurValides() {
        return ResponseEntity.ok(intermediaireService.getRatioExecutionSurValides());
    }

    @GetMapping("/croissance-volume")
    public ResponseEntity<Double> getCroissanceVolumeOperations() {
        return ResponseEntity.ok(intermediaireService.getCroissanceVolumeOperations());
    }

    @GetMapping("/investisseurs-actifs-aujourdhui")
    public ResponseEntity<Long> getInvestisseursActifsAujourdhui() {
        return ResponseEntity.ok(intermediaireService.getInvestisseursActifsAujourdhui());
    }

    @GetMapping("/nouveaux-investisseurs-semaine")
    public ResponseEntity<Long> getNouveauxInvestisseursCetteSemaine() {
        return ResponseEntity.ok(intermediaireService.getNouveauxInvestisseursCetteSemaine());
    }

    @GetMapping("/ordres-non-executables")
    public ResponseEntity<Long> getOrdresNonExecutables() {
        return ResponseEntity.ok(intermediaireService.getOrdresNonExecutables());
    }

    @GetMapping("/commission-2jours")
    public ResponseEntity<Double> getCommissionDerniers2Jours() {
        return ResponseEntity.ok(intermediaireService.getCommissionDerniers2Jours());
    }
    @GetMapping("/actions")
    public List<ActionDTO> getAllActionDtos() {
        return actionService.getAllActionDTOs();
    }
}
