package pfe.application_bourse.mapper;

import org.springframework.stereotype.Component;
import pfe.application_bourse.dto.ordre.OperationDTO;
import pfe.application_bourse.dto.ordre.OperationResponseDTO;
import pfe.application_bourse.dto.ordre.OperationSimulationDTO;
import pfe.application_bourse.entity.ordre.Operation;

import java.util.List;
import java.util.stream.Collectors;

@Component

public class OperationMapper {
    public static OperationDTO toDto(Operation op) {
        OperationDTO dto = new OperationDTO();
        dto.setId(op.getId());
        dto.setDateDeGeneration(op.getDateDeGeneration());
        dto.setQuantiteTotale(op.getQuantiteTotale());
        dto.setMontantTotal(op.getMontantTotal());
        dto.setStatut(op.getStatut().name());
        return dto;
    }

    public OperationSimulationDTO toSimulationDTO(Operation operation) {
        return new OperationSimulationDTO()
                .setQuantiteTotale(operation.getQuantiteTotale())
                .setMontantTotal(operation.getMontantTotal())
                .setCommission(operation.getCommission())
                .setStatut(operation.getStatut().name());
    }
    public OperationResponseDTO toResponseDTO(Operation operation) {
        return new OperationResponseDTO()
                .setId(operation.getId())
                .setQuantiteTotale(operation.getQuantiteTotale())
                .setMontantTotal(operation.getMontantTotal())
                .setDateDeGeneration(operation.getDateDeGeneration())
                .setStatut(operation.getStatut().name());
    }
    public List<OperationSimulationDTO> toSimulationDTOList(List<Operation> operations) {
        return operations.stream().map(this::toSimulationDTO).collect(Collectors.toList());
    }

    public List<OperationResponseDTO> toResponseDTOList(List<Operation> operations) {
        return operations.stream().map(this::toResponseDTO).collect(Collectors.toList());
    }
}
