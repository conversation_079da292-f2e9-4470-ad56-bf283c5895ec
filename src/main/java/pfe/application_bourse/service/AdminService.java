package pfe.application_bourse.service;

import lombok.AllArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import pfe.application_bourse.dto.referentiel.AdminStatsDto;
import pfe.application_bourse.dto.referentiel.GetUser;
import pfe.application_bourse.entity.Role;
import pfe.application_bourse.entity.User;
import pfe.application_bourse.entity.UserRequest;
import pfe.application_bourse.enumeration.RequestStatus;
import pfe.application_bourse.repository.RoleRepository;
import pfe.application_bourse.repository.UserRepository;
import pfe.application_bourse.repository.UserRequestRepository;

import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
public class AdminService {
    private final UserRequestRepository reqRepo;
    private final UserRepository userRepo;
    private final RoleRepository roleRepo;
    private final PasswordEncoder passwordEncoder;
private final EmailService emailService;
    public List<UserRequest> getAllUserRequest() {
        return reqRepo.findAll();
    }

    public Object approve(Long id) {
        System.out.println("🔧 [DEBUG] approve() CALLED for id = " + id);

        Optional<UserRequest> optionalRequest = reqRepo.findById(id);

        if (optionalRequest.isEmpty()) {
            throw new RuntimeException("Demande introuvable avec ID : " + id);
        }

        UserRequest request = optionalRequest.get();


        Role role = roleRepo.findByName(request.getRoleName())
                .orElseThrow(() -> new RuntimeException("Rôle introuvable"));


        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setRole(role);

        userRepo.save(user);

        request.setStatus(RequestStatus.APPROVED);
        reqRepo.save(request);
        emailService.envoyerEmailValidation(user.getEmail());
        return " Utilisateur approuvé et créé ";
    }



    public String reject(Long id) {
        Optional<UserRequest> opt = reqRepo.findById(id);
        if (opt.isEmpty()) return "Request not found";

        UserRequest request = opt.get();
        request.setStatus(RequestStatus.REJECTED);
        reqRepo.save(request);

emailService.envoyerEmailRefus(opt.get().getEmail());
        return "Request rejected";
    }
    public List<UserRequest> getPendingRequests() {
        return reqRepo.findByStatus(RequestStatus.PENDING);
    }

    public AdminStatsDto getAdminStats() {
        long totalUsers = userRepo.count();
        long pendingRequests = reqRepo.countByStatus(RequestStatus.PENDING);
        long totalRoles = roleRepo.count();

        List<AdminStatsDto.SimpleUser> latestUsers = userRepo
                .findTop5ByOrderByCreatedAtDesc()
                .stream()
                .map(u -> {
                    String roleName = (u.getRole() != null) ? u.getRole().getName() : "Aucun rôle";
                    return new AdminStatsDto.SimpleUser(
                            u.getUsername(),
                            u.getEmail(),
                            roleName
                    );
                })
                .toList();


        return new AdminStatsDto(totalUsers, pendingRequests, totalRoles, latestUsers);
    }


}
