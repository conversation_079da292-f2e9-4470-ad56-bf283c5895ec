package pfe.application_bourse.service.ordre;

import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import pfe.application_bourse.dto.ordre.*;
import pfe.application_bourse.entity.Action;
import pfe.application_bourse.entity.Portefeuille;
import pfe.application_bourse.entity.Solde;
import pfe.application_bourse.entity.User;
import pfe.application_bourse.entity.ordre.Operation;
import pfe.application_bourse.entity.ordre.Ordre;
import pfe.application_bourse.entity.ordre.OrdreOperation;
import pfe.application_bourse.entity.ordre.OrdrePropose;
import pfe.application_bourse.enumeration.OrdreType;
import pfe.application_bourse.enumeration.StatutType;
import pfe.application_bourse.mapper.OperationMapper;
import pfe.application_bourse.mapper.OrdreMapper;
import pfe.application_bourse.mapper.OrdreProposeMapper;
import pfe.application_bourse.repository.ActionRepository;
import pfe.application_bourse.repository.PortefeuilleRepository;
import pfe.application_bourse.repository.UserRepository;
import pfe.application_bourse.repository.ordre.OperationRepository;
import pfe.application_bourse.repository.ordre.OrdreOperationRepository;
import pfe.application_bourse.repository.ordre.OrdreProposeRepository;
import pfe.application_bourse.repository.ordre.OrdreRepository;
import pfe.application_bourse.service.PortefeuilleService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class OrdreService {
    private final OrdreRepository ordreRepository;
    private final UserRepository userRepository;
    private final ActionRepository actionRepository;
    private final OrdreMapper ordreMapper;
    private final PortefeuilleRepository portefeuilleRepository;
    private final OrdreProposeRepository ordreProposeRepository;
    private final OperationRepository operationRepository;
    private final OrdreOperationRepository ordreOperationRepository;
    private final OperationMapper operationMapper;
    private final NotificationService notificationService;
    @Value("${intermediaire.operation.quantite.max}")
    private int quantiteMax;

    public List<OrdreResponseDTO> getOrdresValides() {
        return ordreRepository.findOrdresValides().stream().map(OrdreMapper::toDto).collect(Collectors.toList());
    }
    public List<OrdreResponseDTO> getOrdresEnExecution() {
        return ordreRepository.findOrdresEnExecution().stream().map(OrdreMapper::toDto).collect(Collectors.toList());
    }
    public List<OrdreResponseDTO> getOrdresExecute() {
        return ordreRepository.findOrdresExecute().stream().map(OrdreMapper::toDto).collect(Collectors.toList());
    }
    public List<OrdreResponseDTO> getOrdresSimilaires(UUID ordreId) {
        Ordre ordre = ordreRepository.findById(ordreId)
                .orElseThrow(() -> new RuntimeException("Ordre non trouvé"));

        List<Ordre> similaires = ordreRepository.findOrdresSimilaires(
                ordre.getType(),
                ordre.getAction().getId(),
                ordre.getDateDeValidite()
        );

        return similaires.stream()
                .map(OrdreMapper::toDto)
                .collect(Collectors.toList());
    }

    public Double calculerPrixFinal(List<Ordre> ordres) {
        if (ordres.isEmpty()) throw new IllegalArgumentException("Liste vide");

        return ordres.get(0).getType() == OrdreType.ACHAT
                ? ordres.stream().mapToDouble(Ordre::getPrixMax).min().orElse(0.0)
                : ordres.stream().mapToDouble(Ordre::getPrixMin).max().orElse(0.0);
    }

    public List<OrdreResponseDTO> getAllOrdres() {
        return ordreRepository.findAll().stream()
                .map(OrdreMapper::toDto)
                .collect(Collectors.toList());
    }
    public List<OrdreResponseDTO> getOrdresByInvestisseurUsername(String username) {
        return ordreRepository.findByInvestisseur_Username(username).stream()
                .map(OrdreMapper::toDto)
                .collect(Collectors.toList());
    }
    public List<OrdreResponseDTO> getOrdresByInvestisseurAndStatut(String username, String statut) {
        StatutType statutEnum = StatutType.valueOf(statut.toUpperCase());
        return ordreRepository.findByInvestisseur_UsernameAndStatut(username, statutEnum)
                .stream()
                .map(OrdreMapper::toDto)
                .collect(Collectors.toList());
    }
    @Transactional
    public OrdreProposeResponseDTO modifierOrdre(UUID ordreId, UpdateOrdreRequest request) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();

        OrdrePropose ordre = ordreProposeRepository.findById(ordreId)
                .orElseThrow(() -> new RuntimeException("Ordre introuvable"));

        if (!ordre.getInvestisseur().getUsername().equals(username)) {
            throw new SecurityException("Vous ne pouvez modifier que vos propres ordres.");
        }

        LocalDate nouvelleDate = LocalDate.parse(request.getDateDeValidite());

        if (nouvelleDate.isBefore(LocalDate.now())) {
            throw new IllegalArgumentException("La date de validité ne peut pas être dans le passé.");
        }

        ordre.setDateDeValidite(nouvelleDate);


        // Application des modifications
        ordre.setQuantite(request.getQuantite());
        ordre.setPrixMin(request.getPrixMin());
        ordre.setPrixMax(request.getPrixMax());
        ordre.setToutOuRien(request.isToutOuRien());

        ordre.setDateDeValidite(nouvelleDate);


        ordreProposeRepository.save(ordre);

        return OrdreProposeMapper.toDto(ordre);
    }
    @Transactional
    public OrdreProposeResponseDTO annulerOrdre(UUID ordreId) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();

        OrdrePropose ordre = ordreProposeRepository.findById(ordreId)
                .orElseThrow(() -> new RuntimeException("Ordre introuvable"));

        if (!ordre.getInvestisseur().getUsername().equals(username)) {
            throw new SecurityException("Vous ne pouvez annuler que vos propres ordres.");
        }



        ordreProposeRepository.save(ordre);
        return OrdreProposeMapper.toDto(ordre);
    }
    public KpiOrdreResponse getOrdreKpis(LocalDate start, LocalDate end) {
        long total = ordreRepository.countTotalOrdres(start, end);

        List<Object[]> byType = ordreRepository.countOrdresByType(start, end);
        List<Object[]> byStatut = ordreRepository.countOrdresByStatut(start, end);

        Map<String, Double> repartitionType = byType.stream().collect(Collectors.toMap(
                row -> row[0].toString(),
                row -> ((Long) row[1]) * 100.0 / total
        ));

        Map<String, Double> repartitionStatut = byStatut.stream().collect(Collectors.toMap(
                row -> row[0].toString(),
                row -> ((Long) row[1]) * 100.0 / total
        ));

        long annules = ordreRepository.countAnnules(start, end);

        double tauxAnnulation = total > 0 ? (annules * 100.0 / total) : 0.0;

        KpiOrdreResponse dto = new KpiOrdreResponse();
        dto.setTotalOrdres(total);
        dto.setRepartitionType(repartitionType);
        dto.setRepartitionStatut(repartitionStatut);
        dto.setTauxAnnulation(tauxAnnulation);

        return dto;
    }
    public List<OperationSimulationDTO> simulerFractionnement(UUID ordreId) {
        Ordre ordre = ordreRepository.findById(ordreId)
                .orElseThrow(() -> new RuntimeException("Ordre introuvable"));

        if (ordre.getStatut() != StatutType.VALIDE) {
            throw new IllegalStateException("Ordre non valide pour exécution");
        }

        double prixReference = ordre.getAction().getDernierPrix();
        List<Operation> simulations = new ArrayList<>();

        if (ordre.isToutOuRien() || ordre.getQuantite() <= quantiteMax) {
            double montant = prixReference * ordre.getQuantite();
            Operation op = new Operation()
                    .setQuantiteTotale(ordre.getQuantite())
                    .setMontantTotal(montant)
                    .setStatut(StatutType.TRAITE);
            simulations.add(op);
        } else {
            double quantiteRestante = ordre.getQuantite();
            while (quantiteRestante > 0) {
                double qte = Math.min(quantiteRestante, quantiteMax);
                double montant = prixReference * qte;
                double commission = montant * 0.04;
                Operation op = new Operation()
                        .setQuantiteTotale(qte)
                        .setMontantTotal(montant)
                        .setStatut(StatutType.TRAITE)
                        .setCommission(commission);

                simulations.add(op);
                quantiteRestante -= qte;
            }
        }

        return operationMapper.toSimulationDTOList(simulations);
    }
    @Transactional
    public List<OperationResponseDTO> validerEtEnvoyerFractionnement(UUID ordreId, int indexEnvoye) {
        Ordre ordre = ordreRepository.findById(ordreId)
                .orElseThrow(() -> new RuntimeException("Ordre introuvable"));

        List<OperationSimulationDTO> simulations = simulerFractionnement(ordreId);
        List<Operation> sauvegardees = new ArrayList<>();

        for (int i = 0; i < simulations.size(); i++) {
            Operation op = new Operation()
                    .setQuantiteTotale(simulations.get(i).getQuantiteTotale())
                    .setMontantTotal(simulations.get(i).getMontantTotal())
                    .setStatut(StatutType.TRAITE)
                    .setDateDeGeneration(LocalDateTime.now())
                    .setCommission(simulations.get(i).getCommission());


            Operation savedOp = operationRepository.save(op);

            OrdreOperation oo = new OrdreOperation()
                    .setOrdre(ordre)
                    .setOperation(savedOp)
                    .setQuantiteAffectee(op.getQuantiteTotale());

            ordreOperationRepository.save(oo);
            savedOp.getOrdreOperations().add(oo);
            sauvegardees.add(savedOp);
        }

        // Envoyer uniquement celle sélectionnée
        if (indexEnvoye < sauvegardees.size()) {
            EnvoyerOperation(sauvegardees.get(indexEnvoye));
        }

        return operationMapper.toResponseDTOList(sauvegardees);
    }
    @Transactional
    public void EnvoyerOperation(Operation operation) {
        if (operation.getStatut() != StatutType.TRAITE) {
            throw new IllegalStateException("Opération non exécutable : statut invalide");
        }

        operation.setStatut(StatutType.EN_EXECUTION);
        operationRepository.save(operation);
        List<OrdreOperation> ordreOps = operation.getOrdreOperations();
        if (ordreOps == null || ordreOps.isEmpty()) return;

        for (OrdreOperation oo : ordreOps) {
            Ordre ordre = oo.getOrdre();
            ordre.setStatut(StatutType.EN_EXECUTION);
            ordreRepository.save(ordre);
            User investisseur = ordre.getInvestisseur();
            String typeTxt = ordre.getType() == OrdreType.ACHAT ? "d'achat" : "de vente";
            Double quantite = oo.getQuantiteAffectee();
            Double montant = oo.getOperation().getMontantTotal();

            String message = String.format(
                    "Votre ordre %s pour l'action %s a été fractionné et une première opération est en cours d’exécution :\n📦 Quantité : %.2f\n💰 Montant : %.2f",
                    typeTxt,
                    ordre.getAction().getNom(),
                    quantite,
                    montant
            );

            notificationService.envoyerNotification(investisseur, message);


        }
    }
    @Transactional
    public Operation regrouperOrdresDansOperation(List<Ordre> ordres) {
        Double prixFinal = calculerPrixFinal(ordres);
        Double quantiteTotale = ordres.stream().mapToDouble(Ordre::getQuantite).sum();
        Double commission = prixFinal * 0.04;
        Operation operation = new Operation();
        operation.setDateDeGeneration(LocalDateTime.now());
        operation.setMontantTotal(prixFinal);
        operation.setQuantiteTotale(quantiteTotale);
        operation.setStatut(StatutType.EN_EXECUTION);
        operation.setCommission(commission);

        operation = operationRepository.save(operation);

        Set<UUID> investisseursNotifies = new HashSet<>();


        // Associer les ordres à l’opération
        for (Ordre ordre : ordres) {
            Ordre ordreFromDb = ordreRepository.findById(ordre.getId())
                    .orElseThrow(() -> new EntityNotFoundException("Ordre introuvable"));

            ordreFromDb.setStatut(StatutType.EN_EXECUTION);
            ordreRepository.save(ordreFromDb);
            double quantiteAffectee = ordreFromDb.getQuantite();

            OrdreOperation ordreOperation = new OrdreOperation()
                    .setQuantiteAffectee(quantiteAffectee)
                    .setOrdre(ordreFromDb)
                    .setOperation(operation);
            ordreOperationRepository.save(ordreOperation);

            User investisseur = ordreFromDb.getInvestisseur();
            if (investisseur != null) {
                UUID userId = investisseur.getId();
                if (!investisseursNotifies.contains(userId)) {
                    String msg = "Votre ordre #" + ordreFromDb.getId() +
                            " a été regroupé dans une opération en exécution (ID opération: " + operation.getId() + ")";
                    notificationService.envoyerNotification(investisseur, msg);
                    investisseursNotifies.add(userId);
                }
            }
        }

        return  operation ;
    }
    @Transactional
    public Operation regrouperOrdresSimilairesDansOperation(UUID ordreId) {
        Ordre ordre = ordreRepository.findById(ordreId)
                .orElseThrow(() -> new EntityNotFoundException("Ordre non trouvé"));

        // Récupérer les ordres similaires
        List<Ordre> ordresSimilaires = ordreRepository.findOrdresSimilaires(
                ordre.getType(),
                ordre.getAction().getId(),
                ordre.getDateDeValidite()
        );

        if (ordresSimilaires.isEmpty()) {
            throw new IllegalStateException("Aucun ordre similaire trouvé à regrouper.");
        }

        return regrouperOrdresDansOperation(ordresSimilaires);
    }


}
